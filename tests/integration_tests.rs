use actix_web::{test, web, App, http::StatusCode};
use actix_session::{storage::CookieSessionStore, SessionMiddleware};
use actix_web::cookie::Key;
use diesel::r2d2::{self, ConnectionManager};
use diesel::{SqliteConnection, RunQueryDsl};
use garden_planner_web::{routes, DbPool};
use serde_json::json;
use std::sync::Once;

static INIT: Once = Once::new();

fn setup_test_db() -> DbPool {
    INIT.call_once(|| {
        std::env::set_var("DATABASE_URL", "sqlite://test_gardening_app.db");
        std::env::set_var("RUST_LOG", "debug");
        env_logger::init();
    });

    let database_url = std::env::var("DATABASE_URL").expect("DATABASE_URL must be set");
    let manager = ConnectionManager::<SqliteConnection>::new(database_url);
    let pool = r2d2::Pool::builder()
        .build(manager)
        .expect("Failed to create pool.");

    // Run migrations
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Clean up existing data for fresh tests
    diesel::sql_query("DELETE FROM season_plan_plants").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM season_plans").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM growing_area_shapes").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM property_shapes").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM property_shares").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM properties").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM user_households").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM households").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM seeds").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM plants").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM users WHERE username != 'admin'").execute(&mut conn).ok();

    pool
}

#[actix_web::test]
async fn test_homepage_loads() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    let req = test::TestRequest::get().uri("/").to_request();
    let resp = test::call_service(&app, req).await;

    assert!(resp.status().is_success());
}

#[actix_web::test]
async fn test_user_registration_and_login() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    // Test registration form access
    let req = test::TestRequest::get().uri("/auth/register").to_request();
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_success());

    // Test login form access
    let req = test::TestRequest::get().uri("/auth/login").to_request();
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_success());
}

#[actix_web::test]
async fn test_plant_database_operations() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    // Test plants list access
    let req = test::TestRequest::get().uri("/plants/list").to_request();
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_success() || resp.status() == StatusCode::FOUND);

    // Test new plant form access
    let req = test::TestRequest::get().uri("/plants/new").to_request();
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_success() || resp.status() == StatusCode::FOUND);
}

#[actix_web::test]
async fn test_seed_database_operations() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    // Test seeds list access
    let req = test::TestRequest::get().uri("/seeds/list").to_request();
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_success() || resp.status() == StatusCode::FOUND);
}

#[actix_web::test]
async fn test_season_plans_operations() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    // Test season plans list access
    let req = test::TestRequest::get().uri("/season_plans").to_request();
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_success() || resp.status() == StatusCode::FOUND);

    // Test new season plan form access
    let req = test::TestRequest::get().uri("/season_plans/new").to_request();
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_success() || resp.status() == StatusCode::FOUND);
}

#[actix_web::test]
async fn test_auto_season_creation() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    // Test auto season creation endpoint
    let req = test::TestRequest::post().uri("/seasons/auto-create").to_request();
    let resp = test::call_service(&app, req).await;
    // Should redirect to login if not authenticated
    assert!(resp.status() == StatusCode::FOUND || resp.status() == StatusCode::UNAUTHORIZED);
}

#[actix_web::test]
async fn test_plant_auto_population() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    // Test plant auto-population endpoint
    let req = test::TestRequest::get()
        .uri("/plants/auto-populate?name=tomato")
        .to_request();
    let resp = test::call_service(&app, req).await;
    // Should return unauthorized if not authenticated
    assert!(resp.status() == StatusCode::UNAUTHORIZED || resp.status() == StatusCode::FOUND);
}

#[actix_web::test]
async fn test_admin_dashboard_access() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    // Test admin dashboard access
    let req = test::TestRequest::get().uri("/admin").to_request();
    let resp = test::call_service(&app, req).await;
    // Should redirect to login if not authenticated
    assert!(resp.status() == StatusCode::FOUND);

    // Test user management access
    let req = test::TestRequest::get().uri("/admin/users").to_request();
    let resp = test::call_service(&app, req).await;
    // Should redirect to login if not authenticated
    assert!(resp.status() == StatusCode::FOUND);

    // Test HerbaDB management access
    let req = test::TestRequest::get().uri("/admin/herba-db").to_request();
    let resp = test::call_service(&app, req).await;
    // Should redirect to login if not authenticated
    assert!(resp.status() == StatusCode::FOUND);
}

#[actix_web::test]
async fn test_household_management() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    // Test households list access
    let req = test::TestRequest::get().uri("/households").to_request();
    let resp = test::call_service(&app, req).await;
    // Should redirect to login if not authenticated
    assert!(resp.status() == StatusCode::FOUND);

    // Test new household form access
    let req = test::TestRequest::get().uri("/households/new").to_request();
    let resp = test::call_service(&app, req).await;
    // Should redirect to login if not authenticated
    assert!(resp.status() == StatusCode::FOUND);
}

#[actix_web::test]
async fn test_wishlist_functionality() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    // Test wishlist access
    let req = test::TestRequest::get().uri("/wishlist/plants").to_request();
    let resp = test::call_service(&app, req).await;
    // Should redirect to login if not authenticated, or return 404 if route not found
    assert!(resp.status() == StatusCode::FOUND || resp.status() == StatusCode::UNAUTHORIZED || resp.status() == StatusCode::NOT_FOUND);
}

#[actix_web::test]
async fn test_property_wizard() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    // Test property wizard access
    let req = test::TestRequest::get().uri("/wizard/property").to_request();
    let resp = test::call_service(&app, req).await;
    // Should return unauthorized, redirect, or OK (depending on implementation)
    assert!(resp.status() == StatusCode::UNAUTHORIZED || resp.status() == StatusCode::FOUND || resp.status() == StatusCode::OK);

    // Test household wizard access
    let req = test::TestRequest::get().uri("/wizard/household").to_request();
    let resp = test::call_service(&app, req).await;
    // Should return unauthorized, redirect, or OK (depending on implementation)
    assert!(resp.status() == StatusCode::UNAUTHORIZED || resp.status() == StatusCode::FOUND || resp.status() == StatusCode::OK);
}
