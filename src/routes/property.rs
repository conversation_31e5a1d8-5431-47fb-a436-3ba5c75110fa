use actix_web::{web, HttpResponse, Responder, get, post};
use actix_session::Session;
use serde::Deserialize;
use serde_json::json;
use diesel::prelude::*;
use crate::DbPool;
use crate::models::property::{Property, NewProperty};
use crate::models::property_shape::{PropertyShape, NewPropertyShape};
use crate::models::growing_area_shape::{GrowingAreaShape, NewGrowingAreaShape};

use crate::utils::auth::is_authenticated;
use crate::utils::templates::{render_template, render_template_with_context};



pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/property")
            .service(index)
            .service(new_property)
            .service(view_property)
            .service(edit_property)
            .service(update_property)
            .service(delete_property)
            .service(wizard_start)
            .service(wizard_step1_post)
            .service(wizard_step2)
            .service(wizard_step2_post)
            .service(wizard_step3)
            .service(wizard_step3_post)
            .service(wizard_step4)
            .service(wizard_finish)
    );
}

#[get("")]
async fn index(pool: web::Data<DbPool>, session: Session) -> impl Responder {
    if !is_authenticated(&session) {
        return HttpResponse::Found().append_header(("Location", "/auth/login")).finish();
    }

    let user_id = session.get::<i32>("user_id").unwrap().unwrap();
    let mut conn = pool.get().expect("Failed to get DB connection");

    // Get all properties for this user
    let properties = Property::find_by_user_id(&mut conn, user_id).unwrap_or_else(|_| Vec::new());

    let mut context = tera::Context::from_serialize(json!({
        "properties": properties
    }))
    .unwrap();

    render_template_with_context("property/index.html", &mut context, &session).unwrap()
}

#[get("/new")]
async fn new_property(session: Session) -> impl Responder {
    if !is_authenticated(&session) {
        return HttpResponse::Found().append_header(("Location", "/auth/login")).finish();
    }

    let context = tera::Context::new();
    render_template("property/new.html", &context).unwrap()
}

#[get("/{id}/view")]
async fn view_property(
    pool: web::Data<DbPool>,
    path: web::Path<i32>,
    session: Session,
    query: web::Query<std::collections::HashMap<String, String>>
) -> impl Responder {
    if !is_authenticated(&session) {
        return HttpResponse::Found().append_header(("Location", "/auth/login")).finish();
    }

    let property_id = path.into_inner();
    let mut conn = pool.get().expect("Failed to get DB connection");

    // Get property
    let property = match Property::find_by_id(&mut conn, property_id) {
        Ok(Some(property)) => property,
        _ => return HttpResponse::NotFound().body("Property not found"),
    };

    // Get current floor from query parameter
    let current_floor: i32 = query.get("floor")
        .and_then(|f| f.parse().ok())
        .unwrap_or(-1); // Default to outside

    let current_floor_name = if current_floor == -1 {
        "Outside".to_string()
    } else {
        format!("Floor {}", current_floor)
    };

    // Load property shapes for the current floor
    use crate::schema::property_shapes;
    let property_shapes = property_shapes::table
        .filter(property_shapes::property_id.eq(property_id))
        .filter(property_shapes::floor_no.eq(current_floor))
        .select(PropertyShape::as_select())
        .load(&mut conn)
        .unwrap_or_default();

    // Load growing area shapes for the current floor
    use crate::schema::growing_area_shapes;
    let growing_area_shapes = growing_area_shapes::table
        .filter(growing_area_shapes::property_id.eq(property_id))
        .filter(growing_area_shapes::floor_no.eq(current_floor))
        .select(GrowingAreaShape::as_select())
        .load(&mut conn)
        .unwrap_or_default();

    // Calculate total growing area
    let total_growing_area: f32 = growing_area_shapes.iter()
        .map(|shape| shape.area.unwrap_or(0.0))
        .sum();

    // Separate inside and outside areas
    let outside_growing_area: f32 = growing_area_shapes.iter()
        .filter(|shape| shape.floor_no == -1)
        .map(|shape| shape.area.unwrap_or(0.0))
        .sum();

    let inside_growing_area = total_growing_area - outside_growing_area;

    let context = tera::Context::from_serialize(json!({
        "property": property,
        "total_growing_area": total_growing_area,
        "outside_growing_area": outside_growing_area,
        "inside_growing_area": inside_growing_area,
        "current_floor": current_floor,
        "current_floor_name": current_floor_name,
        "growing_areas": growing_area_shapes,
        "property_shapes": property_shapes,
        "growing_areas_json": growing_area_shapes
    }))
    .unwrap();

    render_template("property/view.html", &context).unwrap()
}

#[get("/{id}/edit")]
async fn edit_property(
    pool: web::Data<DbPool>,
    path: web::Path<i32>,
    session: Session
) -> impl Responder {
    if !is_authenticated(&session) {
        return HttpResponse::Found().append_header(("Location", "/auth/login")).finish();
    }

    let property_id = path.into_inner();
    let mut conn = pool.get().expect("Failed to get DB connection");

    // Get property
    let property = match Property::find_by_id(&mut conn, property_id) {
        Ok(Some(property)) => property,
        _ => return HttpResponse::NotFound().body("Property not found"),
    };

    let context = tera::Context::from_serialize(json!({
        "property": property
    }))
    .unwrap();

    render_template("property/edit.html", &context).unwrap()
}

#[post("/{id}/update")]
async fn update_property(
    pool: web::Data<DbPool>,
    path: web::Path<i32>,
    form: web::Form<PropertyForm>,
    session: Session
) -> impl Responder {
    if !is_authenticated(&session) {
        return HttpResponse::Found().append_header(("Location", "/auth/login")).finish();
    }

    let property_id = path.into_inner();
    let mut conn = pool.get().expect("Failed to get DB connection");

    // Update the property
    use crate::schema::properties::dsl::*;
    let result = diesel::update(properties.filter(id.eq(Some(property_id))))
        .set((
            name.eq(&form.name),
            floors.eq(form.floors),
            inside_area.eq(form.inside_area),
            outside_area.eq(form.outside_area),
            household_id.eq(form.household_id),
        ))
        .execute(&mut conn);

    match result {
        Ok(_) => HttpResponse::Found()
            .append_header(("Location", format!("/property/{}/view", property_id)))
            .finish(),
        Err(_) => HttpResponse::InternalServerError().body("Failed to update property"),
    }
}

#[post("/{id}/delete")]
async fn delete_property(
    pool: web::Data<DbPool>,
    path: web::Path<i32>,
    session: Session
) -> impl Responder {
    if !is_authenticated(&session) {
        return HttpResponse::Found().append_header(("Location", "/auth/login")).finish();
    }

    let property_id = path.into_inner();
    let mut conn = pool.get().expect("Failed to get DB connection");

    // Delete the property
    use crate::schema::properties::dsl::*;
    let result = diesel::delete(properties.filter(id.eq(Some(property_id))))
        .execute(&mut conn);

    match result {
        Ok(_) => HttpResponse::Found()
            .append_header(("Location", "/property"))
            .finish(),
        Err(_) => HttpResponse::InternalServerError().body("Failed to delete property"),
    }
}

// Wizard routes
#[get("/wizard")]
async fn wizard_start(session: Session) -> impl Responder {
    if !is_authenticated(&session) {
        return HttpResponse::Found().append_header(("Location", "/auth/login")).finish();
    }

    // Clear any existing wizard data
    session.remove("wizard_property");
    session.remove("wizard_boundary");
    session.remove("wizard_growing_areas");

    let context = tera::Context::from_serialize(json!({
        "step": 1
    }))
    .unwrap();

    render_template("property/wizard.html", &context).unwrap()
}

#[derive(Deserialize)]
struct PropertyForm {
    name: String,
    floors: i32,
    inside_area: Option<f32>,
    outside_area: Option<f32>,
    household_id: i32,
}

#[derive(Deserialize)]
struct WizardPropertyForm {
    name: String,
    floors: i32,
    #[serde(rename = "type")]
    property_type: String,
}

#[post("/wizard/step1")]
async fn wizard_step1_post(
    session: Session,
    form: web::Form<WizardPropertyForm>
) -> impl Responder {
    if !is_authenticated(&session) {
        return HttpResponse::Found().append_header(("Location", "/auth/login")).finish();
    }

    // Store property data in session
    let property_data = json!({
        "name": form.name,
        "floors": form.floors,
        "property_type": form.property_type
    });

    session.insert("wizard_property", property_data).unwrap();

    HttpResponse::Found()
        .append_header(("Location", "/property/wizard/step2"))
        .finish()
}

#[get("/wizard/step2")]
async fn wizard_step2(session: Session) -> impl Responder {
    if !is_authenticated(&session) {
        return HttpResponse::Found().append_header(("Location", "/auth/login")).finish();
    }

    // Check if we have property data
    if session.get::<serde_json::Value>("wizard_property").unwrap().is_none() {
        return HttpResponse::Found()
            .append_header(("Location", "/property/wizard"))
            .finish();
    }

    let context = tera::Context::from_serialize(json!({
        "step": 2
    }))
    .unwrap();

    render_template("property/wizard.html", &context).unwrap()
}

#[derive(Deserialize)]
struct ShapesData {
    shapes: Vec<serde_json::Value>,
}

#[post("/wizard/step2")]
async fn wizard_step2_post(
    session: Session,
    data: web::Json<ShapesData>
) -> impl Responder {
    if !is_authenticated(&session) {
        return web::Json(json!({
            "success": false,
            "error": "Not authenticated"
        }));
    }

    // Store boundary shapes in session
    session.insert("wizard_boundary", data.shapes.clone()).unwrap();

    web::Json(json!({
        "success": true,
        "redirect": "/property/wizard/step3"
    }))
}

#[get("/wizard/step3")]
async fn wizard_step3(session: Session) -> impl Responder {
    if !is_authenticated(&session) {
        return HttpResponse::Found().append_header(("Location", "/auth/login")).finish();
    }

    // Check if we have boundary data
    if session.get::<Vec<serde_json::Value>>("wizard_boundary").unwrap().is_none() {
        return HttpResponse::Found()
            .append_header(("Location", "/property/wizard/step2"))
            .finish();
    }

    let boundary = session.get::<Vec<serde_json::Value>>("wizard_boundary").unwrap().unwrap();

    let context = tera::Context::from_serialize(json!({
        "step": 3,
        "property_boundary": boundary
    }))
    .unwrap();

    render_template("property/wizard.html", &context).unwrap()
}

#[post("/wizard/step3")]
async fn wizard_step3_post(
    session: Session,
    data: web::Json<ShapesData>
) -> impl Responder {
    if !is_authenticated(&session) {
        return web::Json(json!({
            "success": false,
            "error": "Not authenticated"
        }));
    }

    // Store growing area shapes in session
    session.insert("wizard_growing_areas", data.shapes.clone()).unwrap();

    web::Json(json!({
        "success": true,
        "redirect": "/property/wizard/step4"
    }))
}

#[get("/wizard/step4")]
async fn wizard_step4(session: Session) -> impl Responder {
    if !is_authenticated(&session) {
        return HttpResponse::Found().append_header(("Location", "/auth/login")).finish();
    }

    // Check if we have growing area data
    if session.get::<Vec<serde_json::Value>>("wizard_growing_areas").unwrap().is_none() {
        return HttpResponse::Found()
            .append_header(("Location", "/property/wizard/step3"))
            .finish();
    }

    // Get all wizard data
    let property_data = session.get::<serde_json::Value>("wizard_property").unwrap().unwrap();
    let boundary = session.get::<Vec<serde_json::Value>>("wizard_boundary").unwrap().unwrap();
    let growing_areas = session.get::<Vec<serde_json::Value>>("wizard_growing_areas").unwrap().unwrap();

    // Calculate areas
    let total_area = calculate_total_area(&boundary);
    let growing_area = calculate_total_area(&growing_areas);
    let utilization_rate = if total_area > 0.0 {
        (growing_area / total_area * 100.0).round()
    } else {
        0.0
    };

    // Create property object for review
    let property = json!({
        "name": property_data["name"],
        "type": property_data["property_type"],
        "floors": property_data["floors"],
        "total_area": total_area,
        "growing_area": growing_area,
        "utilization_rate": utilization_rate
    });

    // Create property data for visualization
    let property_data_viz = json!({
        "boundary": boundary,
        "growingAreas": growing_areas,
        "floors": property_data["floors"]
    });

    let context = tera::Context::from_serialize(json!({
        "step": 4,
        "property": property,
        "property_data": property_data_viz
    }))
    .unwrap();

    render_template("property/wizard.html", &context).unwrap()
}

#[post("/wizard/finish")]
async fn wizard_finish(
    pool: web::Data<DbPool>,
    session: Session
) -> impl Responder {
    if !is_authenticated(&session) {
        return web::Json(json!({
            "success": false,
            "error": "Not authenticated"
        }));
    }

    let user_id = session.get::<i32>("user_id").unwrap().unwrap();

    // Get all wizard data
    let property_data = session.get::<serde_json::Value>("wizard_property").unwrap().unwrap();
    let boundary = session.get::<Vec<serde_json::Value>>("wizard_boundary").unwrap().unwrap();
    let growing_areas = session.get::<Vec<serde_json::Value>>("wizard_growing_areas").unwrap().unwrap();

    // Calculate areas
    let total_area = calculate_total_area(&boundary);
    let _growing_area = calculate_total_area(&growing_areas);

    // Create new property
    let mut conn = pool.get().expect("Failed to get DB connection");

    let property_name = property_data["name"].as_str().unwrap();
    let property_to_insert = NewProperty {
        name: property_name,
        floors: property_data["floors"].as_i64().unwrap() as i32,
        household_id: 1, // Default household for now
        owner_id: user_id,
        inside_area: Some(0.0), // Will be updated later
        outside_area: Some(total_area as f32),
    };

    use crate::schema::properties::dsl::*;
    diesel::insert_into(properties)
        .values(&property_to_insert)
        .execute(&mut conn)
        .expect("Failed to create property");

    // Get the last inserted property ID
    let last_id: i64 = diesel::select(diesel::dsl::sql::<diesel::sql_types::BigInt>("last_insert_rowid()"))
        .get_result(&mut conn)
        .expect("Failed to get property ID");
    let property_id = last_id as i32;

    // Save property boundary shapes
    {
        use crate::schema::property_shapes;
        for shape in boundary {
            let points_json = serde_json::to_string(&shape["points"]).unwrap();

            let new_shape = NewPropertyShape {
                property_id,
                shape_data: &points_json,
                shape_type: "boundary",
                floor_no: 0, // Ground floor
                area: Some(0.0f64), // Calculate later if needed
            };

            diesel::insert_into(property_shapes::table)
                .values(&new_shape)
                .execute(&mut conn)
                .expect("Failed to save property shape");
        }
    }

    // Save growing area shapes
    {
        use crate::schema::growing_area_shapes;
        for shape in growing_areas {
            let points_json = serde_json::to_string(&shape["points"]).unwrap();

            let new_shape = NewGrowingAreaShape {
                property_id,
                shape_data: &points_json,
                shape_type: "growing_area",
                floor_no: 0, // Ground floor
                area: Some(0.0), // Will be calculated later
            };

            diesel::insert_into(growing_area_shapes::table)
                .values(&new_shape)
                .execute(&mut conn)
                .expect("Failed to save growing area shape");
        }
    }

    // Clear wizard data
    session.remove("wizard_property");
    session.remove("wizard_boundary");
    session.remove("wizard_growing_areas");

    web::Json(json!({
        "success": true,
        "redirect": format!("/property/{}/view", property_id)
    }))
}

// Helper function to calculate total area from shapes
fn calculate_total_area(shapes: &Vec<serde_json::Value>) -> f64 {
    let mut total_area = 0.0;

    for shape in shapes {
        if let Some(points) = shape["points"].as_array() {
            let mut area = 0.0;
            let n = points.len();

            if n < 3 {
                continue;
            }

            for i in 0..n-1 {
                let x1 = points[i]["x"].as_f64().unwrap_or(0.0);
                let y1 = points[i]["y"].as_f64().unwrap_or(0.0);
                let x2 = points[i+1]["x"].as_f64().unwrap_or(0.0);
                let y2 = points[i+1]["y"].as_f64().unwrap_or(0.0);

                area += (x1 * y2) - (x2 * y1);
            }

            // Close the polygon
            let x1 = points[n-1]["x"].as_f64().unwrap_or(0.0);
            let y1 = points[n-1]["y"].as_f64().unwrap_or(0.0);
            let x2 = points[0]["x"].as_f64().unwrap_or(0.0);
            let y2 = points[0]["y"].as_f64().unwrap_or(0.0);

            area += (x1 * y2) - (x2 * y1);
            area = area.abs() / 2.0;

            total_area += area;
        }
    }

    // Convert from pixel area to square feet (approximate)
    total_area / 100.0
}


