use reqwest;
use scraper::{Html, Selector};
use serde_json::json;
use std::collections::HashMap;
use crate::models::HerbaPlant;
use crate::models::herba_plant::NewHerbaPlant;
use diesel::SqliteConnection;
use regex::Regex;
use serde::{Serialize, Deserialize};

/// Automatic herba information gatherer that fetches plant data from multiple sources
pub struct HerbaGatherer {
    client: reqwest::Client,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlantIdentifier {
    pub latin_name: Option<String>,
    pub common_name: Option<String>,
    pub partial_name: Option<String>,
    pub family: Option<String>,
    pub genus: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GatheredPlantData {
    pub latin_name: String,
    pub common_name: String,
    pub family: Option<String>,
    pub genus: Option<String>,
    pub species: Option<String>,
    pub variety: Option<String>,
    pub description: Option<String>,
    pub growth_habit: Option<String>,
    pub mature_height: Option<String>,
    pub mature_width: Option<String>,
    pub sun_requirements: Option<String>,
    pub water_requirements: Option<String>,
    pub soil_type: Option<String>,
    pub soil_ph: Option<String>,
    pub hardiness_zone: Option<String>,
    pub bloom_time: Option<String>,
    pub bloom_color: Option<String>,
    pub foliage_color: Option<String>,
    pub fruit_time: Option<String>,
    pub companion_plants: Option<String>,
    pub antagonist_plants: Option<String>,
    pub pest_resistance: Option<String>,
    pub disease_resistance: Option<String>,
    pub edible_parts: Option<String>,
    pub medicinal_uses: Option<String>,
    pub culinary_uses: Option<String>,
    pub propagation_methods: Option<String>,
    pub seed_germination_time: Option<i32>,
    pub seed_viability: Option<i32>,
    pub days_to_maturity: Option<i32>,
    pub harvest_method: Option<String>,
    pub storage_method: Option<String>,
    pub nutritional_info: Option<String>,
}

impl HerbaGatherer {
    pub fn new() -> Self {
        Self {
            client: reqwest::Client::new(),
        }
    }

    /// Main entry point for gathering plant information
    pub async fn gather_plant_info(&self, identifier: PlantIdentifier) -> Result<GatheredPlantData, Box<dyn std::error::Error>> {
        // Try multiple sources in order of preference
        let mut gathered_data = None;

        // 1. Try Wikipedia first (most comprehensive)
        if let Ok(data) = self.gather_from_wikipedia(&identifier).await {
            gathered_data = Some(data);
        }

        // 2. Try USDA Plants Database
        if gathered_data.is_none() {
            if let Ok(data) = self.gather_from_usda(&identifier).await {
                gathered_data = Some(data);
            }
        }

        // 3. Try Missouri Botanical Garden
        if gathered_data.is_none() {
            if let Ok(data) = self.gather_from_missouri_botanical(&identifier).await {
                gathered_data = Some(data);
            }
        }

        // 4. Try PlantNet API
        if gathered_data.is_none() {
            if let Ok(data) = self.gather_from_plantnet(&identifier).await {
                gathered_data = Some(data);
            }
        }

        // 5. Fallback to built-in plant database
        if gathered_data.is_none() {
            gathered_data = Some(self.create_basic_plant_data(&identifier));
        }

        gathered_data.ok_or("Failed to gather plant information from any source".into())
    }

    /// Gather information from Wikipedia
    async fn gather_from_wikipedia(&self, identifier: &PlantIdentifier) -> Result<GatheredPlantData, Box<dyn std::error::Error>> {
        let search_term = self.get_best_search_term(identifier);
        let url = format!("https://en.wikipedia.org/wiki/{}", search_term.replace(" ", "_"));

        let response = self.client.get(&url).send().await?;
        let html = response.text().await?;
        let document = Html::parse_document(&html);

        let mut data = GatheredPlantData {
            latin_name: identifier.latin_name.clone().unwrap_or_else(|| search_term.clone()),
            common_name: identifier.common_name.clone().unwrap_or_else(|| search_term.clone()),
            family: None,
            genus: None,
            species: None,
            variety: None,
            description: None,
            growth_habit: None,
            mature_height: None,
            mature_width: None,
            sun_requirements: None,
            water_requirements: None,
            soil_type: None,
            soil_ph: None,
            hardiness_zone: None,
            bloom_time: None,
            bloom_color: None,
            foliage_color: None,
            fruit_time: None,
            companion_plants: None,
            antagonist_plants: None,
            pest_resistance: None,
            disease_resistance: None,
            edible_parts: None,
            medicinal_uses: None,
            culinary_uses: None,
            propagation_methods: None,
            seed_germination_time: None,
            seed_viability: None,
            days_to_maturity: None,
            harvest_method: None,
            storage_method: None,
            nutritional_info: None,
        };

        // Extract taxonomic information from infobox
        if let Ok(infobox_selector) = Selector::parse(".infobox") {
            for infobox in document.select(&infobox_selector) {
                data.family = self.extract_infobox_value(&infobox, "Family");
                data.genus = self.extract_infobox_value(&infobox, "Genus");
                data.species = self.extract_infobox_value(&infobox, "Species");
            }
        }

        // Extract description from first paragraph
        if let Ok(p_selector) = Selector::parse("p") {
            if let Some(first_p) = document.select(&p_selector).next() {
                data.description = Some(first_p.text().collect::<Vec<_>>().join(" ").chars().take(500).collect());
            }
        }

        // Extract cultivation information
        data.sun_requirements = self.extract_cultivation_info(&document, &["sun", "light", "exposure"]);
        data.water_requirements = self.extract_cultivation_info(&document, &["water", "moisture", "irrigation"]);
        data.soil_type = self.extract_cultivation_info(&document, &["soil", "substrate"]);

        Ok(data)
    }

    /// Gather information from USDA Plants Database
    async fn gather_from_usda(&self, identifier: &PlantIdentifier) -> Result<GatheredPlantData, Box<dyn std::error::Error>> {
        let search_term = self.get_best_search_term(identifier);
        let url = format!("https://plants.usda.gov/home/<USER>", search_term.to_uppercase());

        // This is a simplified implementation - in reality you'd need to handle USDA's specific API
        let response = self.client.get(&url).send().await?;
        let html = response.text().await?;
        let document = Html::parse_document(&html);

        let mut data = self.create_basic_plant_data(identifier);

        // Extract USDA-specific information
        data.hardiness_zone = self.extract_text_by_label(&document, "Hardiness Zone");
        data.growth_habit = self.extract_text_by_label(&document, "Growth Habit");
        data.mature_height = self.extract_text_by_label(&document, "Height");

        Ok(data)
    }

    /// Gather information from Missouri Botanical Garden
    async fn gather_from_missouri_botanical(&self, identifier: &PlantIdentifier) -> Result<GatheredPlantData, Box<dyn std::error::Error>> {
        let search_term = self.get_best_search_term(identifier);
        let url = format!("https://www.missouribotanicalgarden.org/PlantFinder/PlantFinderSearch.aspx?searchtext={}", search_term);

        let response = self.client.get(&url).send().await?;
        let html = response.text().await?;
        let document = Html::parse_document(&html);

        let mut data = self.create_basic_plant_data(identifier);

        // Extract Missouri Botanical Garden specific information
        data.bloom_time = self.extract_text_by_label(&document, "Bloom Time");
        data.bloom_color = self.extract_text_by_label(&document, "Flower Color");
        data.sun_requirements = self.extract_text_by_label(&document, "Sun");
        data.water_requirements = self.extract_text_by_label(&document, "Water");

        Ok(data)
    }

    /// Gather information from PlantNet API
    async fn gather_from_plantnet(&self, identifier: &PlantIdentifier) -> Result<GatheredPlantData, Box<dyn std::error::Error>> {
        // PlantNet API integration would go here
        // For now, return basic data
        Ok(self.create_basic_plant_data(identifier))
    }

    /// Create basic plant data from identifier
    pub fn create_basic_plant_data(&self, identifier: &PlantIdentifier) -> GatheredPlantData {
        let latin_name = identifier.latin_name.clone()
            .or_else(|| identifier.common_name.clone())
            .or_else(|| identifier.partial_name.clone())
            .unwrap_or_else(|| "Unknown plant".to_string());

        let common_name = identifier.common_name.clone()
            .or_else(|| identifier.latin_name.clone())
            .or_else(|| identifier.partial_name.clone())
            .unwrap_or_else(|| "Unknown plant".to_string());

        GatheredPlantData {
            latin_name,
            common_name,
            family: identifier.family.clone(),
            genus: identifier.genus.clone(),
            species: None,
            variety: None,
            description: Some("Plant information gathered automatically".to_string()),
            growth_habit: Some("annual".to_string()), // Default assumption
            mature_height: Some("30-60 cm".to_string()),
            mature_width: Some("20-40 cm".to_string()),
            sun_requirements: Some("full sun".to_string()),
            water_requirements: Some("medium".to_string()),
            soil_type: Some("well-drained".to_string()),
            soil_ph: Some("6.0-7.0".to_string()),
            hardiness_zone: Some("5-9".to_string()),
            bloom_time: Some("summer".to_string()),
            bloom_color: None,
            foliage_color: Some("green".to_string()),
            fruit_time: None,
            companion_plants: Some("[]".to_string()), // Empty JSON array
            antagonist_plants: Some("[]".to_string()), // Empty JSON array
            pest_resistance: Some("moderate".to_string()),
            disease_resistance: Some("moderate".to_string()),
            edible_parts: None,
            medicinal_uses: None,
            culinary_uses: None,
            propagation_methods: Some("seed".to_string()),
            seed_germination_time: Some(7), // 7 days default
            seed_viability: Some(3), // 3 years default
            days_to_maturity: Some(90), // 90 days default
            harvest_method: None,
            storage_method: None,
            nutritional_info: Some("{}".to_string()), // Empty JSON object
        }
    }

    /// Get the best search term from identifier
    fn get_best_search_term(&self, identifier: &PlantIdentifier) -> String {
        identifier.latin_name.clone()
            .or_else(|| identifier.common_name.clone())
            .or_else(|| identifier.partial_name.clone())
            .unwrap_or_else(|| "unknown".to_string())
    }

    /// Extract value from Wikipedia infobox
    fn extract_infobox_value(&self, infobox: &scraper::ElementRef, label: &str) -> Option<String> {
        // This is a simplified implementation
        // In reality, you'd need more sophisticated parsing
        None
    }

    /// Extract cultivation information from document
    fn extract_cultivation_info(&self, document: &Html, keywords: &[&str]) -> Option<String> {
        // This is a simplified implementation
        // In reality, you'd need more sophisticated text analysis
        None
    }

    /// Extract text by label from document
    fn extract_text_by_label(&self, document: &Html, label: &str) -> Option<String> {
        // This is a simplified implementation
        // In reality, you'd need more sophisticated parsing
        None
    }

    /// Save gathered data to database
    pub async fn save_to_database(&self, data: GatheredPlantData, conn: &mut SqliteConnection) -> Result<HerbaPlant, Box<dyn std::error::Error>> {
        let new_herba = NewHerbaPlant {
            latin_name: &data.latin_name,
            common_name: &data.common_name,
            family: data.family.as_deref(),
            genus: data.genus.as_deref(),
            species: data.species.as_deref(),
            variety: data.variety.as_deref(),
            description: data.description.as_deref(),
            growth_habit: data.growth_habit.as_deref(),
            mature_height: data.mature_height.as_deref(),
            mature_width: data.mature_width.as_deref(),
            sun_requirements: data.sun_requirements.as_deref(),
            water_requirements: data.water_requirements.as_deref(),
            soil_type: data.soil_type.as_deref(),
            soil_ph: data.soil_ph.as_deref(),
            hardiness_zone: data.hardiness_zone.as_deref(),
            bloom_time: data.bloom_time.as_deref(),
            bloom_color: data.bloom_color.as_deref(),
            foliage_color: data.foliage_color.as_deref(),
            fruit_time: data.fruit_time.as_deref(),
            companion_plants: data.companion_plants.as_deref(),
            antagonist_plants: data.antagonist_plants.as_deref(),
            pest_resistance: data.pest_resistance.as_deref(),
            disease_resistance: data.disease_resistance.as_deref(),
            edible_parts: data.edible_parts.as_deref(),
            medicinal_uses: data.medicinal_uses.as_deref(),
            culinary_uses: data.culinary_uses.as_deref(),
            propagation_methods: data.propagation_methods.as_deref(),
            seed_germination_time: data.seed_germination_time,
            seed_viability: data.seed_viability,
            days_to_maturity: data.days_to_maturity,
            harvest_method: data.harvest_method.as_deref(),
            storage_method: data.storage_method.as_deref(),
            nutritional_info: data.nutritional_info.as_deref(),
        };

        Ok(HerbaPlant::create(conn, &new_herba)?)
    }
}
