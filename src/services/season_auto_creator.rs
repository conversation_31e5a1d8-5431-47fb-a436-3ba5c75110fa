use chrono::{NaiveDate, Datelike};
use diesel::SqliteConnection;
use crate::models::season::{Season, NewSeason};

/// Service for automatically creating seasons
pub struct SeasonAutoCreator;

impl SeasonAutoCreator {
    pub fn new() -> Self {
        Self
    }

    /// Auto-create seasons for the current year if they don't exist
    pub fn ensure_current_year_seasons(&self, conn: &mut SqliteConnection) -> Result<Vec<i32>, Box<dyn std::error::Error>> {
        let current_year = chrono::Utc::now().year();
        self.ensure_year_seasons(conn, current_year)
    }

    /// Auto-create seasons for a specific year if they don't exist
    pub fn ensure_year_seasons(&self, conn: &mut SqliteConnection, year: i32) -> Result<Vec<i32>, Box<dyn std::error::Error>> {
        // Check if seasons already exist for this year
        if Season::exists_for_year(conn, year)? {
            return Ok(Vec::new());
        }

        let mut created_season_ids = Vec::new();

        // Create full year season
        let full_year_season = NewSeason {
            name: &format!("{} Full Year", year),
            start_date: NaiveDate::from_ymd_opt(year, 1, 1).unwrap(),
            end_date: NaiveDate::from_ymd_opt(year, 12, 31).unwrap(),
        };
        let full_year_id = Season::create(conn, &full_year_season)?;
        created_season_ids.push(full_year_id);

        // Create spring season (March 20 - June 20)
        let spring_season = NewSeason {
            name: &format!("{} Spring", year),
            start_date: NaiveDate::from_ymd_opt(year, 3, 20).unwrap(),
            end_date: NaiveDate::from_ymd_opt(year, 6, 20).unwrap(),
        };
        let spring_id = Season::create(conn, &spring_season)?;
        created_season_ids.push(spring_id);

        // Create summer season (June 21 - September 22)
        let summer_season = NewSeason {
            name: &format!("{} Summer", year),
            start_date: NaiveDate::from_ymd_opt(year, 6, 21).unwrap(),
            end_date: NaiveDate::from_ymd_opt(year, 9, 22).unwrap(),
        };
        let summer_id = Season::create(conn, &summer_season)?;
        created_season_ids.push(summer_id);

        // Create fall season (September 23 - December 20)
        let fall_season = NewSeason {
            name: &format!("{} Fall", year),
            start_date: NaiveDate::from_ymd_opt(year, 9, 23).unwrap(),
            end_date: NaiveDate::from_ymd_opt(year, 12, 20).unwrap(),
        };
        let fall_id = Season::create(conn, &fall_season)?;
        created_season_ids.push(fall_id);

        // Create winter season (December 21 - March 19 next year)
        let winter_season = NewSeason {
            name: &format!("{} Winter", year),
            start_date: NaiveDate::from_ymd_opt(year, 12, 21).unwrap(),
            end_date: NaiveDate::from_ymd_opt(year + 1, 3, 19).unwrap(),
        };
        let winter_id = Season::create(conn, &winter_season)?;
        created_season_ids.push(winter_id);

        Ok(created_season_ids)
    }

    /// Create a custom season with specified dates
    pub fn create_custom_season(
        &self,
        conn: &mut SqliteConnection,
        name: &str,
        start_date: NaiveDate,
        end_date: NaiveDate,
    ) -> Result<i32, Box<dyn std::error::Error>> {
        let custom_season = NewSeason {
            name,
            start_date,
            end_date,
        };
        
        let season_id = Season::create(conn, &custom_season)?;
        Ok(season_id)
    }

    /// Auto-create seasons for next year
    pub fn ensure_next_year_seasons(&self, conn: &mut SqliteConnection) -> Result<Vec<i32>, Box<dyn std::error::Error>> {
        let next_year = chrono::Utc::now().year() + 1;
        self.ensure_year_seasons(conn, next_year)
    }

    /// Get or create current season based on today's date
    pub fn get_current_season(&self, conn: &mut SqliteConnection) -> Result<Option<Season>, Box<dyn std::error::Error>> {
        let today = chrono::Utc::now().date_naive();
        let current_year = today.year();
        
        // Ensure seasons exist for current year
        self.ensure_year_seasons(conn, current_year)?;
        
        // Find the season that contains today's date
        let seasons = Season::find_by_year(conn, current_year)?;
        
        for season in seasons {
            if today >= season.start_date && today <= season.end_date {
                return Ok(Some(season));
            }
        }
        
        Ok(None)
    }
}
