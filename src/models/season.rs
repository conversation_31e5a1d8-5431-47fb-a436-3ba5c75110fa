use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use chrono::NaiveDate;

use crate::schema::seasons;

#[derive(Debug, Clone, Queryable, Identifiable, Serialize, Deserialize, AsChangeset)]
#[diesel(table_name = seasons)]
pub struct Season {
    pub id: i32,
    pub name: String,
    pub start_date: NaiveDate,
    pub end_date: NaiveDate,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = seasons)]
pub struct NewSeason<'a> {
    pub name: &'a str,
    pub start_date: NaiveDate,
    pub end_date: NaiveDate,
}

impl Season {
    pub fn find_all(conn: &mut SqliteConnection) -> QueryResult<Vec<Season>> {
        seasons::table.load::<Season>(conn)
    }

    pub fn find_by_id(conn: &mut SqliteConnection, season_id: i32) -> QueryResult<Option<Season>> {
        seasons::table
            .filter(seasons::id.eq(season_id))
            .first(conn)
            .optional()
    }

    pub fn create(conn: &mut SqliteConnection, new_season: &NewSeason) -> QueryResult<i32> {
        use crate::schema::seasons::dsl::*;

        diesel::insert_into(seasons)
            .values(new_season)
            .execute(conn)?;

        // Get the last inserted row ID
        let last_id: i64 = diesel::select(diesel::dsl::sql::<diesel::sql_types::BigInt>("last_insert_rowid()"))
            .get_result(conn)?;

        Ok(last_id as i32)
    }

    pub fn find_by_year(conn: &mut SqliteConnection, year: i32) -> QueryResult<Vec<Season>> {
        use chrono::NaiveDate;
        let year_start = NaiveDate::from_ymd_opt(year, 1, 1).unwrap();
        let year_end = NaiveDate::from_ymd_opt(year, 12, 31).unwrap();

        seasons::table
            .filter(seasons::start_date.ge(year_start))
            .filter(seasons::start_date.le(year_end))
            .load::<Season>(conn)
    }

    pub fn exists_for_year(conn: &mut SqliteConnection, year: i32) -> QueryResult<bool> {
        let seasons = Self::find_by_year(conn, year)?;
        Ok(!seasons.is_empty())
    }
}
