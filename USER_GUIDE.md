# Garden Planner Web Application - User Guide

## Table of Contents
1. [Getting Started](#getting-started)
2. [User Registration & Authentication](#user-registration--authentication)
3. [Navigation & Interface](#navigation--interface)
4. [Property Management](#property-management)
5. [Drawing Tools](#drawing-tools)
6. [Household Management](#household-management)
7. [Plant & Seed Database](#plant--seed-database)
8. [Wishlist System](#wishlist-system)
9. [Season Planning](#season-planning)
10. [Admin Features](#admin-features)
11. [Troubleshooting](#troubleshooting)

## Getting Started

### System Requirements
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Internet connection
- JavaScript enabled

### First Time Setup
1. Navigate to the Garden Planner application URL
2. Click "Register" to create a new account
3. **Important**: The first user to register becomes a superadmin automatically
4. Fill in your username and password
5. Click "Register" to complete setup

## User Registration & Authentication

### Creating an Account
- Click "Register" from the homepage
- Choose a unique username
- Create a secure password
- Submit the form

### Logging In
- Click "Login" from the homepage
- Enter your credentials
- You'll be redirected to the main dashboard

### User Roles
- **Superadmin**: Full system access, can manage all users and features
- **Admin**: Can manage users and access admin features
- **Moderator**: Can access HerbaDB management
- **User**: Standard access to personal features

## Navigation & Interface

### Global Navigation Bar
The top navigation bar is accessible from all pages and includes:

#### Left Side - Logo
- **🌱 Garden Planner**: Click to return to homepage

#### Center - Main Navigation Links
- **Plants**: Access plant database
- **Seeds**: Manage seed collection
- **Plots**: View and manage garden plots
- **Properties**: Property management and drawing tools
- **Households**: Household sharing and collaboration
- **Seasons**: Season management
- **Season Plans**: Automated and manual season planning
- **Notifications**: View watering and care reminders
- **Wishlist**: Plant and seed wishlist management
- **Admin**: Admin dashboard (admin users only)

#### Right Side - User Controls
- **Theme Toggle**: Switch between light and dark mode (sun/moon icon)
- **User Menu**: Click your avatar to access:
  - Profile settings
  - Account settings
  - Household management
  - Logout

### Mobile Navigation
- On mobile devices, navigation collapses to a hamburger menu
- All features remain accessible through the mobile menu

## Property Management

### Creating a New Property
1. Navigate to **Properties** → **Create New Property**
2. Fill in basic property information:
   - Property name
   - Number of floors
   - Location details
3. Click "Next" to proceed to the drawing wizard

### Property Drawing Wizard

#### Step 1: Draw Property Boundaries
1. **Select Drawing Tool**:
   - **Freehand**: Draw custom shapes by clicking and dragging
   - **Rectangle**: Click and drag to create rectangular areas
   - **Circle**: Click and drag to create circular areas

2. **Grid Toggle**: Click the grid button to show/hide the alignment grid

3. **Drawing Tips**:
   - Freehand tool follows your cursor precisely
   - Use grid for precise alignment
   - Multiple shapes can be drawn for complex properties

4. **Area Calculation**: The system automatically calculates area in square meters

5. **Saving**: Click "Save Shape" when finished drawing

#### Step 2: Draw Growing Areas
1. Property boundaries appear dimmed as reference
2. Draw growing areas within property bounds
3. Growing areas are highlighted in green
4. System ensures growing areas stay within property boundaries

### Viewing Properties
1. Go to **Properties** → **View Properties**
2. Click on any property to view details
3. **Floor Navigation**: Use floor selector to view different levels
4. **Visual Layout**: See property boundaries and growing areas
5. **Growing Area Details**: View area calculations and usage

## Drawing Tools

### Tool Selection
- **Freehand**: Most flexible, follows cursor exactly
- **Rectangle**: Perfect for square/rectangular areas
- **Circle**: Ideal for circular growing areas

### Grid System
- **Toggle**: Single-click the grid button to show/hide
- **Alignment**: Helps with precise shape placement
- **Snap**: Some tools snap to grid for accuracy

### Shape Management
- **Erase Last**: Remove the most recently drawn shape
- **Start Over**: Clear all current shapes
- **Saved Shapes**: Previously saved shapes appear dimmed

### Best Practices
1. **Start with Property Boundaries**: Always draw the overall property first
2. **Use Grid for Precision**: Enable grid for accurate measurements
3. **Plan Growing Areas**: Consider sun exposure and access when drawing
4. **Save Frequently**: Save shapes as you complete sections

## Household Management

### Creating a Household
1. Navigate to **Households** → **Create New Household**
2. Enter household name and details
3. You become the household owner automatically

### Inviting Members
1. Go to your household view
2. Click "Invite Members" (owner only)
3. Share household access with other users
4. Assign appropriate roles

### Household Roles
- **Owner**: Full control, can invite/remove members
- **Admin**: Can manage household settings
- **Member**: Can view and contribute to household planning
- **Viewer**: Read-only access

### Sharing Features
- **Properties**: Share property access with household members
- **Season Plans**: Collaborate on garden planning
- **Plant Database**: Shared plant and seed collections

## Plant & Seed Database

### Adding Plants
1. Navigate to **Plants** → **Add New Plant**
2. Fill in plant details:
   - Common name
   - Scientific name
   - Growing requirements
   - Care instructions
3. Save the plant to your database

### Adding Seeds
1. Go to **Seeds** → **Add New Seed**
2. Enter seed information:
   - Variety name
   - Source/supplier
   - Planting dates
   - Storage information

### Database Features
- **Search**: Find plants by name or characteristics
- **Filter**: Sort by plant type, season, or requirements
- **Details**: View comprehensive plant information
- **Edit**: Update plant information as needed

## Wishlist System

### Adding to Wishlist
1. Browse **Plants** or **Seeds** sections
2. Click "Add to Wishlist" on desired items
3. Items are saved to your personal wishlist

### Managing Wishlist
1. Navigate to **Wishlist** → **Plants** or **Seeds**
2. View all wishlist items
3. **Remove Items**: Click "Remove from Wishlist"
4. **Priority**: Set priority levels for planning
5. **Notes**: Add personal notes about wishlist items

### Wishlist Features
- **Visual Indicators**: Buttons show current wishlist status
- **Quick Access**: Easy add/remove from any plant/seed page
- **Planning Integration**: Use wishlist for season planning

## Season Planning

### Manual Season Planning
1. Navigate to **Season Plans** → **Create New Plan**
2. Select season and date range
3. Choose plants from your database
4. Assign plants to growing areas
5. Set planting and harvest dates

### Automated Season Planning
1. Go to **Season Plans** → **Auto Planner**
2. Set preferences:
   - Available growing space
   - Preferred plant types
   - Yield goals
3. System generates optimized planting schedule
4. Review and adjust recommendations

### Season Plan Features
- **Crop Rotation**: Automatic nutrient rotation planning
- **Yield Optimization**: Maximize harvest from available space
- **Companion Planting**: Suggest beneficial plant combinations
- **Timeline View**: Visual planting and harvest schedule

## Admin Features

### Admin Dashboard
**Access**: Available to admin, superadmin, and moderator users

1. Navigate to **Admin** from the main menu
2. **Dashboard Overview**:
   - User statistics
   - System metrics
   - Recent activity

### User Management
**Access**: Superadmin only

1. Go to **Admin** → **Users**
2. **View All Users**: See complete user list
3. **Role Management**: Change user roles
4. **User Statistics**: Monitor user activity

### HerbaDB Management
**Access**: Admin, superadmin, and moderator users

1. Navigate to **Admin** → **HerbaDB**
2. **Plant Information Scraper**:
   - Enter plant name (common or scientific)
   - System gathers information from public databases
   - Review and save plant data
3. **Database Maintenance**: Manage plant information quality

### HerbaDB Scraper Usage
1. Go to **Admin** → **HerbaDB**
2. Enter plant identifier:
   - Full common name (e.g., "Melissa officinalis")
   - Scientific name
   - Partial name for search
3. Click "Scrape Information"
4. Review gathered data:
   - Watering needs
   - Nutrient requirements
   - Environmental preferences
   - Growing conditions
5. Save to database for user access

## Troubleshooting

### Common Issues

#### Drawing Tools Not Working
- **Check Browser**: Ensure JavaScript is enabled
- **Refresh Page**: Reload if tools become unresponsive
- **Grid Toggle**: Try toggling grid on/off
- **Tool Selection**: Ensure correct tool is selected

#### Shape Saving Errors
- **Large Shapes**: Reduce shape complexity if getting size errors
- **Network Issues**: Check internet connection
- **Try Again**: Shapes are preserved if save fails

#### Grid Not Visible
- **Single Click**: Grid toggle requires only one click
- **Tool Selection**: Some tools work better with grid enabled
- **Browser Zoom**: Check if browser zoom affects grid display

#### Property View Issues
- **Floor Selection**: Use floor selector to view different levels
- **Data Loading**: Allow time for property data to load
- **Browser Compatibility**: Try different browser if issues persist

#### Household Access Problems
- **Role Permissions**: Check your household role
- **Owner Actions**: Some features require owner permissions
- **Invitation Status**: Ensure you've been properly invited

### Getting Help

#### Admin Support
- Contact your system administrator
- Check admin dashboard for system status
- Report issues through proper channels

#### Feature Requests
- Discuss with household members
- Contact system administrators
- Document specific use cases

### Performance Tips

#### Optimal Usage
- **Regular Saves**: Save work frequently
- **Browser Updates**: Keep browser updated
- **Clear Cache**: Clear browser cache if experiencing issues
- **Stable Connection**: Use stable internet connection for best experience

#### Mobile Usage
- **Touch Interface**: Use touch gestures for drawing
- **Screen Orientation**: Landscape mode recommended for drawing
- **Zoom Controls**: Use pinch-to-zoom for detailed work

## Advanced Features

### Integration Capabilities
- **Weather Data**: System can integrate weather information
- **Notification System**: Automated care reminders
- **Yield Tracking**: Monitor harvest success
- **Growth Analytics**: Track plant performance

### Customization Options
- **Theme Selection**: Light/dark mode toggle
- **Personal Preferences**: Customize dashboard layout
- **Notification Settings**: Control reminder frequency
- **Privacy Controls**: Manage household sharing

## Best Practices

### Property Management
1. **Accurate Measurements**: Use grid for precise area calculation
2. **Logical Organization**: Group related growing areas
3. **Future Planning**: Consider expansion when drawing
4. **Documentation**: Add notes about soil conditions and features

### Household Collaboration
1. **Clear Roles**: Assign appropriate permissions
2. **Regular Communication**: Coordinate planning activities
3. **Shared Goals**: Align on household gardening objectives
4. **Resource Sharing**: Coordinate tool and seed sharing

### Season Planning
1. **Start Early**: Plan seasons well in advance
2. **Consider Climate**: Account for local growing conditions
3. **Succession Planting**: Plan multiple harvests
4. **Record Keeping**: Track what works and what doesn't

## New Advanced Features

### Automatic Season Creation
The system automatically creates seasons for the current year when you first access the seasons page:
- **Full Year** (January 1 - December 31)
- **Spring** (March 20 - June 20)
- **Summer** (June 21 - September 22)
- **Fall** (September 23 - December 20)
- **Winter** (December 21 - March 19)

### Plant Auto-Population
When adding new plants, the system can automatically gather information from public databases:
1. Enter the plant name (common or Latin name)
2. Use the auto-populate feature
3. The system searches multiple botanical databases
4. Plant information is automatically filled in including:
   - Growth requirements
   - Watering and fertilizing needs
   - Companion and antagonist plants
   - Pest and disease resistance

### Enhanced HerbaDB Integration
The application includes a comprehensive plant database (HerbaDB) that:
- Stores scientific plant information from multiple sources
- Links individual plants to meta plant records
- Provides detailed growing information
- Supports multiple plant identifiers (Latin name, common name, partial matches)
- Auto-populates plant data from public databases

### AI-Powered Season Planning
The automatic season planner can:
- Optimize crop placement for maximum yield
- Consider companion planting relationships
- Plan succession plantings for continuous harvests
- Balance soil nutrients through crop rotation
- Account for space requirements and growing conditions

### Enhanced Notification System
Automated notifications help you stay on top of garden care:
- **Watering reminders** based on plant-specific needs
- **Fertilizing schedules** customized per plant type
- **Harvest alerts** when crops are ready
- **Seasonal task reminders** for planting and maintenance

### Multi-Household Support with Role-Based Access
Users can belong to multiple households with different permission levels:
- **Edit Mode**: Full access to modify household data
- **Temporary Care**: Limited access for plant care tasks
- **View Mode**: Read-only access to household information
- **Comment Mode**: Can add comments and notes

### Enhanced User Interface
The application features:
- **Sage-tinted Material 3 styling** for a natural, garden-inspired look
- **Light and dark mode support** with automatic theme switching
- **Plant-like avatars** in user interface elements
- **Responsive design** that works on all devices
- **Touch-friendly interface** optimized for tablets and phones

## Three Main User Scenarios

### 1. Superadmin Creating and Sharing Households
1. **Initial Setup**: First user becomes superadmin automatically
2. **Household Creation**: Create primary household with property layout
3. **User Management**: Invite other users and assign appropriate roles
4. **Sharing Configuration**: Set up household sharing with different permission levels
5. **System Administration**: Manage global settings and user accounts

### 2. Plant Creation with Auto-Populated HerbaDB Data
1. **Plant Addition**: Navigate to Plants → New Plant
2. **Auto-Population**: Enter plant name and trigger auto-population
3. **Data Review**: Review and edit auto-populated information
4. **Wishlist Integration**: Add desired plants to wishlist
5. **HerbaDB Linking**: Individual plants link to meta plant records

### 3. Automatic Season Planning
1. **Season Setup**: Seasons are automatically created for current year
2. **Property Selection**: Choose property and growing areas
3. **Plant Selection**: Select plants from database or wishlist
4. **AI Optimization**: System generates optimal planting plan
5. **Manual Adjustments**: Fine-tune the automated plan as needed
6. **Implementation**: Follow the generated planting schedule

## Data Integration Sources
The system integrates with multiple external sources:
- **Wikipedia** for general plant information
- **USDA Plants Database** for scientific data
- **Missouri Botanical Garden** for cultivation details
- **Royal Horticultural Society** for growing advice

This comprehensive guide covers all major features of the Garden Planner application. The system combines automated intelligence with manual control, giving you the flexibility to garden your way while benefiting from expert knowledge and optimization algorithms. For additional support or advanced features, consult with your system administrator or refer to the in-app help sections.
